#ifndef FORMINPUTAMOUNT_H
#define FORMINPUTAMOUNT_H

#include <QWidget>
#include <QLineEdit>
#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include "baseopwidget.h"

/**
 * @brief 补费金额输入界面
 * 专门用于输入补费金额的独立页面
 */
class FormInputAmount : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormInputAmount(QWidget *parent = 0);
    ~FormInputAmount();

    /**
     * @brief 显示金额输入界面
     * @param currentAmount 当前金额（分）
     * @param vehPlate 车牌号（用于显示）
     * @param vehPlateColor 车牌颜色（用于显示）
     * @param vehType 车型（用于显示和查询最大收费金额）
     * @return true-用户确认输入，false-用户取消
     */
    bool InputAmount(int currentAmount, const QString &vehPlate, int vehPlateColor, int vehType = 1);

    /**
     * @brief 获取输入的金额
     * @return 金额（分）
     */
    int GetInputAmount() const { return m_inputAmount; }

protected:
    // 界面初始化
    void InitUI() ;
    void CreateControls();
    void SetupControlProperties();
    void InitLayout();
    void InitConnections();

    // 事件处理
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent) ;
    void paintEvent(QPaintEvent *event) ;

    // 输入处理
    void OnInputNumber(int keyInput);
    void OnInputDecimalPoint();
    void OnDeleteInput();
    void OnClearInput();

    // 验证
    bool ValidateAmount(QString &errorMsg);
    
    // 辅助方法
    QString GetVehTypeName(int vehType);
    int GetDefaultMaxFeeByVehType(int vehType);
    int GetMaxFeeFromSpParaTable(int vehType);

private slots:
    void OnAmountChanged();
    void OnConfirmClicked();
    void OnCancelClicked();

private:
    // 界面控件
    QLabel *m_pLblTitle;           // 标题
    // 车牌信息（标签 + 内容）
    QLabel *m_pLblVehPlate;        // 车牌标签
    QLabel *m_pLblVehPlateValue;   // 车牌内容
    // 车型信息（标签 + 内容）
    QLabel *m_pLblVehType;         // 车型标签
    QLabel *m_pLblVehTypeValue;    // 车型内容
    QLabel *m_pLblMaxFeeLabel;     // 最大收费金额标签
    QLabel *m_pLblMaxFeeValue;     // 最大收费金额值
    QLabel *m_pLblAmountLabel;     // 补费金额标签
    QLineEdit *m_pEditAmount;      // 补费金额输入框
    QLabel *m_pLblAmountUnit;      // 金额单位
    QLabel *m_pLblHelpInfo;        // 帮助信息

    // 数据
    int m_inputAmount;             // 输入的金额（分）
    QString m_vehPlate;            // 车牌号
    int m_vehPlateColor;           // 车牌颜色
    int m_vehType;                 // 车型
    int m_maxFeeAmount;            // 最大收费金额（分）
    QString m_inputText;           // 当前输入的文本

    // 样式
    QFont m_fontTitle;
    QFont m_fontLabel;
    QFont m_fontEdit;
    QFont m_fontHelp;
    QColor m_colorBackground;
};

#endif // FORMINPUTAMOUNT_H
